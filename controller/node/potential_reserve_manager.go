package node

// PotentialReserveManager handles maximum potential reserves for non-reserved tasks
type PotentialReserveManager struct {
	maxReserve   map[string][]float64
	taskReserves map[int]map[string][]float64 // taskID -> resourceType -> reserves
}

// NewPotentialReserveManager creates a new potential reserve manager
func NewPotentialReserveManager() *PotentialReserveManager {
	return &PotentialReserveManager{
		maxReserve:   make(map[string][]float64),
		taskReserves: make(map[int]map[string][]float64),
	}
}

// InitializeResource initializes max reserve tracking for a resource type
func (prm *PotentialReserveManager) InitializeResource(resourceType string, size int) {
	if prm.maxReserve[resourceType] == nil {
		prm.maxReserve[resourceType] = initializeSlice(size)
	} else {
		slice := prm.maxReserve[resourceType]
		ensureSliceSize(&slice, size)
		prm.maxReserve[resourceType] = slice
	}
}

// AddTaskReserve adds potential reserves for a task and updates max reserves incrementally
func (prm *PotentialReserveManager) AddTaskReserve(taskID int, resourceType string, reserves []float64) {
	if len(reserves) == 0 {
		return
	}

	// Initialize task reserves if needed
	if prm.taskReserves[taskID] == nil {
		prm.taskReserves[taskID] = make(map[string][]float64)
	}

	// Store task reserves
	prm.taskReserves[taskID][resourceType] = copySlice(reserves)

	// Update max reserves incrementally
	maxSlice := prm.maxReserve[resourceType]
	for i, reserve := range reserves {
		if i < len(maxSlice) && reserve > maxSlice[i] {
			maxSlice[i] = reserve
		}
	}
}

// RemoveTaskReserve removes potential reserves for a task
func (prm *PotentialReserveManager) RemoveTaskReserve(taskID int, resourceType string) bool {
	if prm.taskReserves[taskID] == nil {
		return false
	}

	reserves := prm.taskReserves[taskID][resourceType]
	if reserves == nil {
		return false
	}

	// Check if this task had the maximum reserve - if so, we need to recalculate
	needsRecalc := false
	maxSlice := prm.maxReserve[resourceType]
	for i, reserve := range reserves {
		if i < len(maxSlice) && reserve > 0 && reserve >= maxSlice[i] {
			needsRecalc = true
			break
		}
	}

	// Remove task reserves
	delete(prm.taskReserves[taskID], resourceType)
	if len(prm.taskReserves[taskID]) == 0 {
		delete(prm.taskReserves, taskID)
	}

	return needsRecalc
}

// RecalculateMaxReserveForResource recalculates max reserve for a specific resource
func (prm *PotentialReserveManager) RecalculateMaxReserveForResource(resourceType string) {
	maxSlice := prm.maxReserve[resourceType]
	if maxSlice == nil {
		return
	}

	// Reset max reserves for this resource
	for i := range maxSlice {
		maxSlice[i] = 0
	}

	// Recalculate from all tasks for this resource only
	for _, taskResources := range prm.taskReserves {
		reserves := taskResources[resourceType]
		for i, val := range reserves {
			if i < len(maxSlice) && val > maxSlice[i] {
				maxSlice[i] = val
			}
		}
	}
}

// GetMaxReserve returns a copy of max reserves
func (prm *PotentialReserveManager) GetMaxReserve() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range prm.maxReserve {
		result[res] = copySlice(values)
	}
	return result
}

// GetMaxReserveForResource returns max reserve for a specific resource
func (prm *PotentialReserveManager) GetMaxReserveForResource(resourceType string) []float64 {
	return prm.maxReserve[resourceType]
}
