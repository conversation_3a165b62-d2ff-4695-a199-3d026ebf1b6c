package node

import (
	"git.moderntv.eu/mcloud/system/common"
)

// Resources is the main coordinator that maintains API compatibility
type Resources struct {
	// Total available resources on the node
	totalResources map[string][]float64

	// Component managers
	reservationManager      *ReservationManager
	potentialReserveManager *PotentialReserveManager

	// Map of task ID -> resource type -> resource info (for API compatibility)
	tasks map[int]map[string]*TaskResourceInfo
}

func NewResources() *Resources {
	return &Resources{
		totalResources:          make(map[string][]float64),
		reservationManager:      NewReservationManager(),
		potentialReserveManager: NewPotentialReserveManager(),
		tasks:                   make(map[int]map[string]*TaskResourceInfo),
	}
}

func (nr *Resources) UpdateResources(free map[string][]float64, available map[string][]float64) {
	if free == nil {
		return
	}

	// Use available if provided, otherwise use free
	resources := free
	if available != nil {
		resources = available
	}

	// Update total resources and initialize managers
	for res, values := range resources {
		// Update total resources
		nr.totalResources[res] = copySlice(values)

		// Initialize managers for this resource type
		nr.reservationManager.InitializeResource(res, len(values))
		nr.potentialReserveManager.InitializeResource(res, len(values))

		// Handle node reservation (difference between free and available)
		if available != nil {
			freeVals := free[res]
			nodeReservations := make([]float64, len(values))
			hasNodeReservation := false

			for i, availVal := range values {
				if i < len(freeVals) {
					nodeReservation := freeVals[i] - availVal
					if nodeReservation > 0 {
						nodeReservations[i] = nodeReservation
						hasNodeReservation = true
					}
				}
			}

			if hasNodeReservation {
				nr.reservationManager.SetNodeReservation(res, nodeReservations)
			}
		}
	}
}

func (nr *Resources) AddTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	// Remove task first if it exists (update operation)
	nr.RemoveTask(dbNodeTask)

	required := dbNodeTask.OriginTask.RequiredResources
	if required == nil {
		return
	}

	// Initialize task map
	if nr.tasks[taskId] == nil {
		nr.tasks[taskId] = make(map[string]*TaskResourceInfo)
	}

	isReserved := ReservedResources(dbNodeTask)
	selectedRes := dbNodeTask.OriginTask.SelectedResources
	taskStringId := dbNodeTask.OriginTask.Id

	for res, reqValue := range *required {
		if reqValue == 0 {
			continue
		}

		// Get resource dimensions
		totalSlice := nr.totalResources[res]
		if totalSlice == nil {
			continue
		}
		l := len(totalSlice)

		// Initialize task resource info
		taskInfo := &TaskResourceInfo{
			Reserved: initializeSlice(l),
			Reserve:  initializeSlice(l),
		}

		// Calculate resource requirements
		usage := dbNodeTask.ResourceUsage[res]
		if selectedRes != nil {
			if i, has := selectedRes[res]; has && i < l {
				// Calculate reserve amount (required - used)
				var reserveAmount float64
				if len(usage) == l && usage[i] > 0 {
					reserveAmount = reqValue - usage[i]
				} else {
					reserveAmount = reqValue
				}

				if reserveAmount > 0 {
					if isReserved {
						// For reserved tasks, add immediate reservation
						taskInfo.Reserved[i] = reserveAmount
						reservations := initializeSlice(l)
						reservations[i] = reserveAmount
						nr.reservationManager.AddReservation(res, taskStringId, reservations)
					} else {
						// For non-reserved tasks, add potential reserve
						taskInfo.Reserve[i] = reserveAmount
						reserves := initializeSlice(l)
						reserves[i] = reserveAmount
						nr.potentialReserveManager.AddTaskReserve(taskId, res, reserves)
					}
				}
			}
		}

		nr.tasks[taskId][res] = taskInfo
	}
}

func (nr *Resources) RemoveTask(dbNodeTask *common.DbNodeTask) {
	taskId := dbNodeTask.OriginTask.DbId

	taskResources, exists := nr.tasks[taskId]
	if !exists {
		return
	}

	taskStringId := dbNodeTask.OriginTask.Id

	// Remove task's resource usage from managers
	for res, taskInfo := range taskResources {
		if taskInfo == nil {
			continue
		}

		// Remove reserved resources
		if hasReservations(taskInfo.Reserved) {
			nr.reservationManager.RemoveReservation(res, taskStringId, taskInfo.Reserved)
		}

		// Remove potential reserves and check if recalculation is needed
		if hasReservations(taskInfo.Reserve) {
			needsRecalc := nr.potentialReserveManager.RemoveTaskReserve(taskId, res)
			if needsRecalc {
				nr.potentialReserveManager.RecalculateMaxReserveForResource(res)
			}
		}
	}

	delete(nr.tasks, taskId)
}

func (nr *Resources) GetAvailable(resource string) ([]float64, map[string][]float64) {
	totalSlice := nr.totalResources[resource]
	if totalSlice == nil {
		return nil, nil
	}

	// Get reserved and max reserve slices
	totalReserved := nr.reservationManager.GetTotalReserved()[resource]
	maxReserve := nr.potentialReserveManager.GetMaxReserveForResource(resource)

	// Calculate available = total - reserved - maxReserve
	available := make([]float64, len(totalSlice))
	for i := range totalSlice {
		avail := totalSlice[i]

		// Subtract immediate reservations
		if totalReserved != nil && i < len(totalReserved) {
			avail -= totalReserved[i]
		}

		// Subtract maximum reserve
		if maxReserve != nil && i < len(maxReserve) {
			avail -= maxReserve[i]
		}

		if avail < 0 {
			available[i] = 0
		} else {
			available[i] = avail
		}
	}

	return available, nr.reservationManager.GetReservationInfo(resource)
}

// GetTaskResourceUsage returns resource usage for a specific task
func (nr *Resources) GetTaskResourceUsage(taskId int) map[string]*TaskResourceInfo {
	return nr.tasks[taskId]
}

// GetTotalReserved returns total reserved resources
func (nr *Resources) GetTotalReserved() map[string][]float64 {
	return nr.reservationManager.GetTotalReserved()
}

// GetMaxReserve returns maximum reserves
func (nr *Resources) GetMaxReserve() map[string][]float64 {
	return nr.potentialReserveManager.GetMaxReserve()
}
