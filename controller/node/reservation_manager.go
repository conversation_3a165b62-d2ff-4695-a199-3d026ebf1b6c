package node

// ReservationManager handles immediate resource reservations
type ReservationManager struct {
	totalReserved   map[string][]float64
	reservationInfo map[string]map[string][]float64
}

// NewReservationManager creates a new reservation manager
func NewReservationManager() *ReservationManager {
	return &ReservationManager{
		totalReserved:   make(map[string][]float64),
		reservationInfo: make(map[string]map[string][]float64),
	}
}

// InitializeResource initializes reservation tracking for a resource type
func (rm *ReservationManager) InitializeResource(resourceType string, size int) {
	if rm.totalReserved[resourceType] == nil {
		rm.totalReserved[resourceType] = initializeSlice(size)
	} else {
		slice := rm.totalReserved[resourceType]
		ensureSliceSize(&slice, size)
		rm.totalReserved[resourceType] = slice
	}

	if rm.reservationInfo[resourceType] == nil {
		rm.reservationInfo[resourceType] = make(map[string][]float64)
	}
}

// AddReservation adds a reservation for a specific task
func (rm *ReservationManager) AddReservation(resourceType, taskID string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	// Update total reserved
	totalSlice := rm.totalReserved[resourceType]
	for i, reservation := range reservations {
		if i < len(totalSlice) && reservation > 0 {
			totalSlice[i] += reservation
		}
	}

	// Update reservation info
	taskSlice := rm.reservationInfo[resourceType][taskID]
	ensureSliceSize(&taskSlice, len(totalSlice))
	rm.reservationInfo[resourceType][taskID] = taskSlice
	copy(taskSlice, reservations)
}

// RemoveReservation removes a reservation for a specific task
func (rm *ReservationManager) RemoveReservation(resourceType, taskID string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	// Update total reserved
	totalSlice := rm.totalReserved[resourceType]
	for i, reservation := range reservations {
		if i < len(totalSlice) && reservation > 0 {
			totalSlice[i] -= reservation
		}
	}

	// Remove from reservation info
	if rm.reservationInfo[resourceType] != nil {
		delete(rm.reservationInfo[resourceType], taskID)
	}
}

// GetTotalReserved returns a copy of total reserved resources
func (rm *ReservationManager) GetTotalReserved() map[string][]float64 {
	result := make(map[string][]float64)
	for res, values := range rm.totalReserved {
		result[res] = copySlice(values)
	}
	return result
}

// GetReservationInfo returns reservation info for a specific resource
func (rm *ReservationManager) GetReservationInfo(resourceType string) map[string][]float64 {
	return rm.reservationInfo[resourceType]
}

// SetNodeReservation sets node-level reservation (difference between free and available)
func (rm *ReservationManager) SetNodeReservation(resourceType string, reservations []float64) {
	if len(reservations) == 0 {
		return
	}

	nodeSlice := rm.reservationInfo[resourceType]["(node)"]
	ensureSliceSize(&nodeSlice, len(reservations))
	rm.reservationInfo[resourceType]["(node)"] = nodeSlice
	copy(nodeSlice, reservations)
}
